{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.chdir(\"..\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from diffusers.pipelines import FluxPipeline\n", "from src.flux.condition import Condition\n", "from PIL import Image\n", "\n", "from src.flux.generate import generate, seed_everything"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pipe = FluxPipeline.from_pretrained(\n", "    \"black-forest-labs/FLUX.1-dev\", torch_dtype=torch.bfloat16\n", ")\n", "pipe = pipe.to(\"cuda\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pipe.load_lora_weights(\n", "    \"Yuanshi/OminiControl\",\n", "    weight_name=f\"experimental/fill.safetensors\",\n", "    adapter_name=\"fill\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = Image.open(\"assets/monalisa.jpg\").convert(\"RGB\").resize((512, 512))\n", "\n", "masked_image = image.copy()\n", "masked_image.paste((0, 0, 0), (128, 100, 384, 220))\n", "\n", "condition = Condition(\"fill\", masked_image)\n", "\n", "seed_everything()\n", "result_img = generate(\n", "    pipe,\n", "    prompt=\"The <PERSON> is wearing a white VR headset with '<PERSON><PERSON>' written on it.\",\n", "    conditions=[condition],\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1536, 512))\n", "concat_image.paste(image, (0, 0))\n", "concat_image.paste(condition.condition, (512, 0))\n", "concat_image.paste(result_img, (1024, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = Image.open(\"assets/book.jpg\").convert(\"RGB\").resize((512, 512))\n", "\n", "w, h, min_dim = image.size + (min(image.size),)\n", "image = image.crop(\n", "    ((w - min_dim) // 2, (h - min_dim) // 2, (w + min_dim) // 2, (h + min_dim) // 2)\n", ").resize((512, 512))\n", "\n", "\n", "masked_image = image.copy()\n", "masked_image.paste((0, 0, 0), (150, 150, 350, 250))\n", "masked_image.paste((0, 0, 0), (200, 380, 320, 420))\n", "\n", "condition = Condition(\"fill\", masked_image)\n", "\n", "seed_everything()\n", "result_img = generate(\n", "    pipe,\n", "    prompt=\"A yellow book with the word 'OMINI' in large font on the cover. The text 'for FLUX' appears at the bottom.\",\n", "    conditions=[condition],\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1536, 512))\n", "concat_image.paste(image, (0, 0))\n", "concat_image.paste(condition.condition, (512, 0))\n", "concat_image.paste(result_img, (1024, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}