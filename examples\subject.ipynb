{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.chdir(\"..\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from diffusers.pipelines import FluxPipeline\n", "from src.flux.condition import Condition\n", "from PIL import Image\n", "\n", "from src.flux.generate import generate, seed_everything"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pipe = FluxPipeline.from_pretrained(\n", "    \"black-forest-labs/FLUX.1-schnell\", torch_dtype=torch.bfloat16\n", ")\n", "pipe = pipe.to(\"cuda\")\n", "pipe.load_lora_weights(\n", "    \"Yuanshi/OminiControl\",\n", "    weight_name=f\"omini/subject_512.safetensors\",\n", "    adapter_name=\"subject\",\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = Image.open(\"assets/penguin.jpg\").convert(\"RGB\").resize((512, 512))\n", "\n", "condition = Condition(\"subject\", image, position_delta=(0, 32))\n", "\n", "prompt = \"On Christmas evening, on a crowded sidewalk, this item sits on the road, covered in snow and wearing a Christmas hat.\"\n", "\n", "\n", "seed_everything(0)\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", "    num_inference_steps=8,\n", "    height=512,\n", "    width=512,\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1024, 512))\n", "concat_image.paste(image, (0, 0))\n", "concat_image.paste(result_img, (512, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = Image.open(\"assets/tshirt.jpg\").convert(\"RGB\").resize((512, 512))\n", "\n", "condition = Condition(\"subject\", image, position_delta=(0, 32))\n", "\n", "prompt = \"On the beach, a lady sits under a beach umbrella. She's wearing this shirt and has a big smile on her face, with her surfboard hehind her. The sun is setting in the background. The sky is a beautiful shade of orange and purple.\"\n", "\n", "\n", "seed_everything()\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", "    num_inference_steps=8,\n", "    height=512,\n", "    width=512,\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1024, 512))\n", "concat_image.paste(condition.condition, (0, 0))\n", "concat_image.paste(result_img, (512, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = Image.open(\"assets/rc_car.jpg\").convert(\"RGB\").resize((512, 512))\n", "\n", "condition = Condition(\"subject\", image, position_delta=(0, 32))\n", "\n", "prompt = \"A film style shot. On the moon, this item drives across the moon surface. The background is that Earth looms large in the foreground.\"\n", "\n", "seed_everything()\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", "    num_inference_steps=8,\n", "    height=512,\n", "    width=512,\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1024, 512))\n", "concat_image.paste(condition.condition, (0, 0))\n", "concat_image.paste(result_img, (512, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = Image.open(\"assets/clock.jpg\").convert(\"RGB\").resize((512, 512))\n", "\n", "condition = Condition(\"subject\", image, position_delta=(0, 32))\n", "\n", "prompt = \"In a Bauhaus style room, this item is placed on a shiny glass table, with a vase of flowers next to it. In the afternoon sun, the shadows of the blinds are cast on the wall.\"\n", "\n", "seed_everything()\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", "    num_inference_steps=8,\n", "    height=512,\n", "    width=512,\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1024, 512))\n", "concat_image.paste(condition.condition, (0, 0))\n", "concat_image.paste(result_img, (512, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = Image.open(\"assets/oranges.jpg\").convert(\"RGB\").resize((512, 512))\n", "\n", "condition = Condition(\"subject\", image, position_delta=(0, 32))\n", "\n", "prompt = \"A very close up view of this item. It is placed on a wooden table. The background is a dark room, the TV is on, and the screen is showing a cooking show.\"\n", "\n", "seed_everything()\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", "    num_inference_steps=8,\n", "    height=512,\n", "    width=512,\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1024, 512))\n", "concat_image.paste(condition.condition, (0, 0))\n", "concat_image.paste(result_img, (512, 0))\n", "concat_image"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}