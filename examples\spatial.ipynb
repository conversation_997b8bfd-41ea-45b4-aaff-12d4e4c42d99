{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.chdir(\"..\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import torch\n", "from diffusers.pipelines import FluxPipeline\n", "from src.flux.condition import Condition\n", "from PIL import Image\n", "\n", "from src.flux.generate import generate, seed_everything"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["pipe = FluxPipeline.from_pretrained(\n", "    \"black-forest-labs/FLUX.1-dev\", torch_dtype=torch.bfloat16\n", ")\n", "pipe = pipe.to(\"cuda\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["for condition_type in [\"canny\", \"depth\", \"coloring\", \"deblurring\"]:\n", "    pipe.load_lora_weights(\n", "        \"Yuanshi/OminiControl\",\n", "        weight_name=f\"experimental/{condition_type}.safetensors\",\n", "        adapter_name=condition_type,\n", "    )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["image = Image.open(\"assets/coffee.png\").convert(\"RGB\")\n", "\n", "w, h, min_dim = image.size + (min(image.size),)\n", "image = image.crop(\n", "    ((w - min_dim) // 2, (h - min_dim) // 2, (w + min_dim) // 2, (h + min_dim) // 2)\n", ").resize((512, 512))\n", "\n", "prompt = \"In a bright room. A cup of a coffee with some beans on the side. They are placed on a dark wooden table.\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["condition = Condition(\"canny\", image)\n", "\n", "seed_everything()\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1536, 512))\n", "concat_image.paste(image, (0, 0))\n", "concat_image.paste(condition.condition, (512, 0))\n", "concat_image.paste(result_img, (1024, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["condition = Condition(\"depth\", image)\n", "\n", "seed_everything()\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1536, 512))\n", "concat_image.paste(image, (0, 0))\n", "concat_image.paste(condition.condition, (512, 0))\n", "concat_image.paste(result_img, (1024, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["condition = Condition(\"deblurring\", image)\n", "\n", "seed_everything()\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1536, 512))\n", "concat_image.paste(image, (0, 0))\n", "concat_image.paste(condition.condition, (512, 0))\n", "concat_image.paste(result_img, (1024, 0))\n", "concat_image"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["condition = Condition(\"coloring\", image)\n", "\n", "seed_everything()\n", "\n", "result_img = generate(\n", "    pipe,\n", "    prompt=prompt,\n", "    conditions=[condition],\n", ").images[0]\n", "\n", "concat_image = Image.new(\"RGB\", (1536, 512))\n", "concat_image.paste(image, (0, 0))\n", "concat_image.paste(condition.condition, (512, 0))\n", "concat_image.paste(result_img, (1024, 0))\n", "concat_image"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 2}